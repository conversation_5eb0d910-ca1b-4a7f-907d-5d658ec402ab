﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AIMemoryReader
{
    public partial class Form1 : Form
    {
        private Memory memory;

        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                memory = new Memory("engine.exe");
                string charName = memory.ReadString((IntPtr)0x009C9754, 13, Encoding.ASCII);
                charNameLabel.Text = "Character Name: " + charName;
            }
            catch (ArgumentException ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }
        private void executeButton1_Click(object sender, EventArgs e)
        {
            memory.Assembly.Execute<bool>((IntPtr)0x006C0310, System.Runtime.InteropServices.CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
        }

        private void executeButton2_Click(object sender, EventArgs e)
        {
            memory.Assembly.Execute<bool>((IntPtr)0x006CC890, System.Runtime.InteropServices.CallingConvention.Cdecl, "*", "I'm Working", 0x34EB40);
        }

        private void button1_Click(object sender, EventArgs e)
        {
            memory.Write<int>((IntPtr)0x009B5B98, 999);
        }
    }
}