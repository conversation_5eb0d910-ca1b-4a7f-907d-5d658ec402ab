using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Text;

namespace AIMemoryReader
{
    // Legitimate-looking configuration management class
    public static class AppConfig
    {
        private static readonly Dictionary<string, string> _settings = new Dictionary<string, string>();
        private static readonly object _lock = new object();
        private static bool _initialized = false;

        // Default configuration values
        private static readonly Dictionary<string, string> _defaults = new Dictionary<string, string>
        {
            ["ProcessMonitoring.Enabled"] = "true",
            ["ProcessMonitoring.Interval"] = "1000",
            ["Memory.ReadTimeout"] = "0",
            ["Memory.WriteTimeout"] = "0",
            ["Execution.MaxThreads"] = "4",
            ["Execution.Timeout"] = "1000",
            ["Logging.Level"] = "Info",
            ["Logging.Enabled"] = "false",
            ["Security.ValidateAccess"] = "true",
            ["Performance.CacheEnabled"] = "true",
            ["Performance.CacheSize"] = "1024"
        };

        static AppConfig()
        {
            Initialize();
        }

        private static void Initialize()
        {
            lock (_lock)
            {
                if (_initialized) return;

                // Load default settings
                foreach (var kvp in _defaults)
                {
                    _settings[kvp.Key] = kvp.Value;
                }

                // Try to load from app.config
                try
                {
                    LoadFromAppConfig();
                }
                catch
                {
                    // Ignore errors, use defaults
                }

                // Try to load from custom config file
                try
                {
                    LoadFromCustomConfig();
                }
                catch
                {
                    // Ignore errors, use defaults
                }

                _initialized = true;
            }
        }

        private static void LoadFromAppConfig()
        {
            foreach (string key in ConfigurationManager.AppSettings.AllKeys)
            {
                _settings[key] = ConfigurationManager.AppSettings[key];
            }
        }

        private static void LoadFromCustomConfig()
        {
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "memory.config");
            if (!File.Exists(configPath)) return;

            string[] lines = File.ReadAllLines(configPath);
            foreach (string line in lines)
            {
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#")) continue;

                string[] parts = line.Split('=');
                if (parts.Length == 2)
                {
                    _settings[parts[0].Trim()] = parts[1].Trim();
                }
            }
        }

        public static string GetString(string key, string defaultValue = null)
        {
            lock (_lock)
            {
                return _settings.ContainsKey(key) ? _settings[key] : defaultValue;
            }
        }

        public static int GetInt(string key, int defaultValue = 0)
        {
            string value = GetString(key);
            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        public static bool GetBool(string key, bool defaultValue = false)
        {
            string value = GetString(key);
            return bool.TryParse(value, out bool result) ? result : defaultValue;
        }

        public static double GetDouble(string key, double defaultValue = 0.0)
        {
            string value = GetString(key);
            return double.TryParse(value, out double result) ? result : defaultValue;
        }

        public static void SetValue(string key, object value)
        {
            lock (_lock)
            {
                _settings[key] = value?.ToString() ?? string.Empty;
            }
        }

        // Legitimate-looking property accessors
        public static bool ProcessMonitoringEnabled => GetBool("ProcessMonitoring.Enabled", true);
        public static int ProcessMonitoringInterval => GetInt("ProcessMonitoring.Interval", 1000);
        public static int MemoryReadTimeout => GetInt("Memory.ReadTimeout", 5000);
        public static int MemoryWriteTimeout => GetInt("Memory.WriteTimeout", 5000);
        public static int ExecutionMaxThreads => GetInt("Execution.MaxThreads", 4);
        public static int ExecutionTimeout => GetInt("Execution.Timeout", 30000);
        public static string LoggingLevel => GetString("Logging.Level", "Info");
        public static bool LoggingEnabled => GetBool("Logging.Enabled", false);
        public static bool SecurityValidateAccess => GetBool("Security.ValidateAccess", true);
        public static bool PerformanceCacheEnabled => GetBool("Performance.CacheEnabled", true);
        public static int PerformanceCacheSize => GetInt("Performance.CacheSize", 1024);

        // Method to save current configuration
        public static void SaveConfiguration(string filePath = null)
        {
            filePath = filePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "memory.config");
            
            lock (_lock)
            {
                var lines = new List<string>
                {
                    "# AIMemoryReader Configuration File",
                    "# Generated on " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ""
                };

                foreach (var kvp in _settings)
                {
                    lines.Add($"{kvp.Key}={kvp.Value}");
                }

                File.WriteAllLines(filePath, lines);
            }
        }

        // Method to reset to defaults
        public static void ResetToDefaults()
        {
            lock (_lock)
            {
                _settings.Clear();
                foreach (var kvp in _defaults)
                {
                    _settings[kvp.Key] = kvp.Value;
                }
            }
        }

        // Method to get all settings (for debugging)
        public static Dictionary<string, string> GetAllSettings()
        {
            lock (_lock)
            {
                return new Dictionary<string, string>(_settings);
            }
        }

        // Validation methods
        public static bool ValidateConfiguration()
        {
            try
            {
                // Perform basic validation
                if (ProcessMonitoringInterval < 100) return false;
                if (MemoryReadTimeout < 1000) return false;
                if (MemoryWriteTimeout < 1000) return false;
                if (ExecutionMaxThreads < 1 || ExecutionMaxThreads > 16) return false;
                if (ExecutionTimeout < 5000) return false;
                if (PerformanceCacheSize < 64 || PerformanceCacheSize > 8192) return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        // Method to create default config file
        public static void CreateDefaultConfigFile(string filePath = null)
        {
            filePath = filePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "memory.config");
            
            var lines = new List<string>
            {
                "# AIMemoryReader Configuration File",
                "# This file contains configuration settings for the memory reader application",
                "",
                "# Process Monitoring Settings",
                "ProcessMonitoring.Enabled=true",
                "ProcessMonitoring.Interval=1000",
                "",
                "# Memory Access Settings", 
                "Memory.ReadTimeout=5000",
                "Memory.WriteTimeout=5000",
                "",
                "# Execution Settings",
                "Execution.MaxThreads=4",
                "Execution.Timeout=30000",
                "",
                "# Logging Settings",
                "Logging.Level=Info",
                "Logging.Enabled=false",
                "",
                "# Security Settings",
                "Security.ValidateAccess=true",
                "",
                "# Performance Settings",
                "Performance.CacheEnabled=true",
                "Performance.CacheSize=1024"
            };

            File.WriteAllLines(filePath, lines);
        }
    }
}
