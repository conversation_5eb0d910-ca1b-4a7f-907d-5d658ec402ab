using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;

namespace AIMemoryReader
{
    // Disguised as a legitimate code execution utility
    public class CodeExecutor
    {
        private readonly MemoryManager _memoryManager;
        private readonly Random _random = new Random();

        public CodeExecutor(MemoryManager memoryManager)
        {
            _memoryManager = memoryManager ?? throw new ArgumentNullException(nameof(memoryManager));
        }

        // Main execution method with obfuscated parameters
        public T ExecuteFunction<T>(IntPtr functionAddress, params object[] parameters) where T : struct
        {
            return ExecuteFunction<T>(functionAddress, CallingConvention.Cdecl, parameters);
        }

        public T ExecuteFunction<T>(IntPtr functionAddress, CallingConvention convention, params object[] parameters) where T : struct
        {
            if (!_memoryManager.IsConnected)
                throw new InvalidOperationException("Memory manager is not connected to target process");

            var allocatedMemory = new List<IntPtr>();
            IntPtr codeAddress = IntPtr.Zero;

            try
            {
                // Process parameters and allocate memory for strings
                var processedParams = ProcessParameters(parameters, allocatedMemory);
                
                // Generate execution code
                byte[] executionCode = GenerateExecutionCode(functionAddress, processedParams, convention);
                
                // Allocate executable memory
                codeAddress = _memoryManager.AllocateMemory((uint)executionCode.Length, true);
                _memoryManager.WriteBytes(codeAddress, executionCode);
                
                // Execute the code
                return ExecuteCode<T>(codeAddress);
            }
            finally
            {
                // Cleanup allocated memory
                CleanupMemory(codeAddress, allocatedMemory);
            }
        }

        private List<object> ProcessParameters(object[] parameters, List<IntPtr> allocatedMemory)
        {
            var processed = new List<object>();
            
            // Process parameters in reverse order for stack-based calling conventions
            foreach (var param in parameters.Reverse())
            {
                if (param is string stringParam)
                {
                    // Allocate memory for string parameter
                    byte[] stringBytes = Encoding.UTF8.GetBytes(stringParam + "\0");
                    IntPtr stringAddress = _memoryManager.AllocateMemory((uint)stringBytes.Length);
                    allocatedMemory.Add(stringAddress);
                    _memoryManager.WriteBytes(stringAddress, stringBytes);
                    processed.Add(stringAddress);
                }
                else
                {
                    processed.Add(param);
                }
            }
            
            return processed;
        }

        private byte[] GenerateExecutionCode(IntPtr functionAddress, List<object> parameters, CallingConvention convention)
        {
            var codeBuilder = new List<byte>();
            
            // Add some randomized NOPs for obfuscation
            AddRandomNops(codeBuilder);
            
            // Push parameters onto stack
            foreach (var param in parameters)
            {
                PushParameter(codeBuilder, param);
            }
            
            // Call the function
            CallFunction(codeBuilder, functionAddress);
            
            // Clean up stack if needed
            if (convention == CallingConvention.Cdecl && parameters.Count > 0)
            {
                CleanupStack(codeBuilder, parameters.Count);
            }
            
            // Add more randomized NOPs
            AddRandomNops(codeBuilder);
            
            // Return
            codeBuilder.Add(0xC3); // ret
            
            return codeBuilder.ToArray();
        }

        private void AddRandomNops(List<byte> code)
        {
            // Add 1-3 random NOP instructions for obfuscation
            int nopCount = _random.Next(1, 4);
            for (int i = 0; i < nopCount; i++)
            {
                code.Add(0x90); // NOP
            }
        }

        private void PushParameter(List<byte> code, object parameter)
        {
            code.Add(0x68); // push immediate
            
            if (parameter is IntPtr ptr)
            {
                code.AddRange(BitConverter.GetBytes(ptr.ToInt32()));
            }
            else if (parameter is int intValue)
            {
                code.AddRange(BitConverter.GetBytes(intValue));
            }
            else if (parameter is uint uintValue)
            {
                code.AddRange(BitConverter.GetBytes(uintValue));
            }
            else
            {
                throw new ArgumentException($"Unsupported parameter type: {parameter.GetType()}");
            }
        }

        private void CallFunction(List<byte> code, IntPtr functionAddress)
        {
            // mov eax, address
            code.Add(0xB8);
            code.AddRange(BitConverter.GetBytes(functionAddress.ToInt32()));
            
            // call eax
            code.Add(0xFF);
            code.Add(0xD0);
        }

        private void CleanupStack(List<byte> code, int parameterCount)
        {
            // add esp, n*4 (clean up stack for cdecl)
            code.Add(0x83);
            code.Add(0xC4);
            code.Add((byte)(parameterCount * 4));
        }

        private T ExecuteCode<T>(IntPtr codeAddress) where T : struct
        {
            // Create and execute remote thread
            IntPtr threadHandle = ProcessHelper.CreateThreadDynamic(
                _memoryManager.TargetProcess.Handle,
                IntPtr.Zero,
                0,
                codeAddress,
                IntPtr.Zero,
                0,
                out IntPtr threadId);

            if (threadHandle == IntPtr.Zero)
                throw new InvalidOperationException("Failed to create remote thread");

            try
            {
                // Wait for thread completion
                uint waitResult = ProcessHelper.WaitForObjectDynamic(threadHandle, 5000); // 5 second timeout
                
                if (waitResult != 0) // WAIT_OBJECT_0
                    throw new TimeoutException("Thread execution timed out");

                // Get thread context to retrieve return value
                var context = new CONTEXT();
                context.ContextFlags = CONTEXT_FLAGS.CONTEXT_INTEGER;
                
                if (!ProcessHelper.GetContextDynamic(threadHandle, ref context))
                    throw new InvalidOperationException("Failed to get thread context");

                // Return value is typically in EAX register
                return _memoryManager.ReadValue<T>((IntPtr)context.Eax);
            }
            finally
            {
                ProcessHelper.CloseHandleDynamic(threadHandle);
            }
        }

        private void CleanupMemory(IntPtr codeAddress, List<IntPtr> allocatedMemory)
        {
            // Free code memory
            if (codeAddress != IntPtr.Zero)
            {
                _memoryManager.FreeMemory(codeAddress);
            }
            
            // Free parameter memory
            foreach (var ptr in allocatedMemory)
            {
                _memoryManager.FreeMemory(ptr);
            }
        }

        // Utility method for simple function calls without return values
        public void ExecuteVoidFunction(IntPtr functionAddress, params object[] parameters)
        {
            ExecuteFunction<int>(functionAddress, parameters);
        }

        public void ExecuteVoidFunction(IntPtr functionAddress, CallingConvention convention, params object[] parameters)
        {
            ExecuteFunction<int>(functionAddress, convention, parameters);
        }

        // Batch execution for multiple function calls
        public void ExecuteBatch(params (IntPtr address, object[] parameters)[] functions)
        {
            foreach (var (address, parameters) in functions)
            {
                ExecuteVoidFunction(address, parameters);
                
                // Add small delay between executions to avoid detection
                Thread.Sleep(_random.Next(10, 50));
            }
        }

        // Advanced execution with custom assembly code
        public T ExecuteCustomCode<T>(byte[] assemblyCode) where T : struct
        {
            if (assemblyCode == null || assemblyCode.Length == 0)
                throw new ArgumentException("Assembly code cannot be null or empty");

            IntPtr codeAddress = IntPtr.Zero;
            
            try
            {
                // Allocate executable memory
                codeAddress = _memoryManager.AllocateMemory((uint)assemblyCode.Length, true);
                _memoryManager.WriteBytes(codeAddress, assemblyCode);
                
                // Execute the code
                return ExecuteCode<T>(codeAddress);
            }
            finally
            {
                if (codeAddress != IntPtr.Zero)
                {
                    _memoryManager.FreeMemory(codeAddress);
                }
            }
        }
    }
}
