using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;

namespace AIMemoryReader
{
    // Disguised as a legitimate memory management utility
    public class MemoryManager : IDisposable
    {
        private IntPtr _processHandle;
        private Process _targetProcess;
        private readonly object _syncLock = new object();
        private bool _disposed = false;

        // Constants disguised as configuration values
        private const int READ_ACCESS = 0x0010;
        private const int WRITE_ACCESS = 0x0020;
        private const int OPERATION_ACCESS = 0x0008;
        private const uint MEM_COMMIT = 0x1000;
        private const uint PAGE_EXECUTE_READWRITE = 0x40;
        private const uint MEM_RELEASE = 0x8000;

        public Process TargetProcess => _targetProcess;
        public bool IsConnected => _processHandle != IntPtr.Zero && !_targetProcess?.HasExited == true;

        // Legitimate-looking constructor
        public MemoryManager(string processName)
        {
            if (string.IsNullOrEmpty(processName))
                throw new ArgumentException("Process name cannot be null or empty");

            InitializeConnection(processName);
        }

        private void InitializeConnection(string processName)
        {
            // Remove .exe extension if present
            string cleanName = processName.Replace(".exe", "");
            
            // Find target process with error handling
            Process[] processes = Process.GetProcessesByName(cleanName);
            if (processes.Length == 0)
                throw new InvalidOperationException($"Process '{processName}' not found");

            _targetProcess = processes[0];
            
            // Establish connection using dynamic API calls
            int accessRights = READ_ACCESS | WRITE_ACCESS | OPERATION_ACCESS;
            _processHandle = ProcessHelper.OpenProcessDynamic(accessRights, false, _targetProcess.Id);
            
            if (_processHandle == IntPtr.Zero)
                throw new UnauthorizedAccessException("Failed to open process - insufficient privileges");
        }

        // Generic data reading with type safety
        public T ReadValue<T>(IntPtr address) where T : struct
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            
            lock (_syncLock)
            {
                int size = Marshal.SizeOf(typeof(T));
                byte[] buffer = new byte[size];
                
                if (!ProcessHelper.ReadMemoryDynamic(_processHandle, address, buffer, size, out int bytesRead))
                    throw new InvalidOperationException("Memory read operation failed");

                if (bytesRead != size)
                    throw new InvalidOperationException($"Partial read: expected {size} bytes, got {bytesRead}");

                return BytesToStruct<T>(buffer);
            }
        }

        // String reading with encoding support
        public string ReadText(IntPtr address, int maxLength, Encoding encoding = null)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            if (maxLength <= 0) throw new ArgumentException("Length must be positive");
            
            encoding = encoding ?? Encoding.UTF8;
            
            lock (_syncLock)
            {
                byte[] buffer = new byte[maxLength];
                
                if (!ProcessHelper.ReadMemoryDynamic(_processHandle, address, buffer, maxLength, out int bytesRead))
                    throw new InvalidOperationException("String read operation failed");

                string result = encoding.GetString(buffer, 0, bytesRead);
                
                // Handle null termination
                int nullIndex = result.IndexOf('\0');
                return nullIndex >= 0 ? result.Substring(0, nullIndex) : result;
            }
        }

        // Raw byte array reading
        public byte[] ReadBytes(IntPtr address, int count)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            if (count <= 0) throw new ArgumentException("Count must be positive");
            
            lock (_syncLock)
            {
                byte[] buffer = new byte[count];
                
                if (!ProcessHelper.ReadMemoryDynamic(_processHandle, address, buffer, count, out int bytesRead))
                    throw new InvalidOperationException("Byte read operation failed");

                if (bytesRead != count)
                {
                    byte[] result = new byte[bytesRead];
                    Array.Copy(buffer, result, bytesRead);
                    return result;
                }
                
                return buffer;
            }
        }

        // Generic data writing with validation
        public void WriteValue<T>(IntPtr address, T value) where T : struct
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            
            lock (_syncLock)
            {
                byte[] buffer = StructToBytes(value);
                
                if (!ProcessHelper.WriteMemoryDynamic(_processHandle, address, buffer, buffer.Length, out int bytesWritten))
                    throw new InvalidOperationException("Memory write operation failed");

                if (bytesWritten != buffer.Length)
                    throw new InvalidOperationException($"Partial write: expected {buffer.Length} bytes, wrote {bytesWritten}");
            }
        }

        // String writing with encoding support
        public void WriteText(IntPtr address, string text, Encoding encoding = null)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            if (text == null) throw new ArgumentNullException(nameof(text));
            
            encoding = encoding ?? Encoding.UTF8;
            
            lock (_syncLock)
            {
                byte[] buffer = encoding.GetBytes(text + "\0");
                
                if (!ProcessHelper.WriteMemoryDynamic(_processHandle, address, buffer, buffer.Length, out int bytesWritten))
                    throw new InvalidOperationException("String write operation failed");

                if (bytesWritten != buffer.Length)
                    throw new InvalidOperationException($"Partial write: expected {buffer.Length} bytes, wrote {bytesWritten}");
            }
        }

        // Raw byte array writing
        public void WriteBytes(IntPtr address, byte[] data)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (data.Length == 0) throw new ArgumentException("Data cannot be empty");
            
            lock (_syncLock)
            {
                if (!ProcessHelper.WriteMemoryDynamic(_processHandle, address, data, data.Length, out int bytesWritten))
                    throw new InvalidOperationException("Byte write operation failed");

                if (bytesWritten != data.Length)
                    throw new InvalidOperationException($"Partial write: expected {data.Length} bytes, wrote {bytesWritten}");
            }
        }

        // Memory allocation wrapper
        public IntPtr AllocateMemory(uint size, bool executable = false)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryManager));
            if (size == 0) throw new ArgumentException("Size must be greater than zero");
            
            lock (_syncLock)
            {
                uint protection = executable ? PAGE_EXECUTE_READWRITE : 0x04; // PAGE_READWRITE
                IntPtr result = ProcessHelper.AllocateMemoryDynamic(_processHandle, IntPtr.Zero, size, MEM_COMMIT, protection);
                
                if (result == IntPtr.Zero)
                    throw new OutOfMemoryException("Failed to allocate memory in target process");
                
                return result;
            }
        }

        // Memory deallocation wrapper
        public bool FreeMemory(IntPtr address)
        {
            if (_disposed) return false;
            if (address == IntPtr.Zero) return false;
            
            lock (_syncLock)
            {
                return ProcessHelper.FreeMemoryDynamic(_processHandle, address, 0, MEM_RELEASE);
            }
        }

        // Utility methods for data conversion
        private static T BytesToStruct<T>(byte[] bytes) where T : struct
        {
            GCHandle handle = GCHandle.Alloc(bytes, GCHandleType.Pinned);
            try
            {
                return (T)Marshal.PtrToStructure(handle.AddrOfPinnedObject(), typeof(T));
            }
            finally
            {
                handle.Free();
            }
        }

        private static byte[] StructToBytes<T>(T value) where T : struct
        {
            int size = Marshal.SizeOf(typeof(T));
            byte[] buffer = new byte[size];
            
            IntPtr ptr = Marshal.AllocHGlobal(size);
            try
            {
                Marshal.StructureToPtr(value, ptr, true);
                Marshal.Copy(ptr, buffer, 0, size);
            }
            finally
            {
                Marshal.FreeHGlobal(ptr);
            }
            
            return buffer;
        }

        // IDisposable implementation
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (_processHandle != IntPtr.Zero)
                {
                    ProcessHelper.CloseHandleDynamic(_processHandle);
                    _processHandle = IntPtr.Zero;
                }
                
                _disposed = true;
            }
        }

        ~MemoryManager()
        {
            Dispose(false);
        }
    }
}
