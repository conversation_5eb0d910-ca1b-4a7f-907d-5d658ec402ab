using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using AIMemoryReader;

namespace AIM<PERSON>oryReader.Examples
{
    public partial class StealthExecutionExample : Form
    {
        private Memory memory;

        public StealthExecutionExample()
        {
            InitializeComponent();
        }

        private void StealthExecutionExample_Load(object sender, EventArgs e)
        {
            try
            {
                memory = new Memory("engine.exe");
                string charName = memory.ReadString((IntPtr)0x009C9754, 13, System.Text.Encoding.ASCII);
                MessageBox.Show("Character Name: " + charName, "Connected Successfully");
            }
            catch (ArgumentException ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        // Example 1: Standard execution (your current method)
        private void StandardExecutionButton_Click(object sender, EventArgs e)
        {
            try
            {
                bool result = memory.Assembly.Execute<bool>(
                    (IntPtr)0x006C0310, 
                    CallingConvention.Cdecl, 
                    "Hello", 0, 0, 0, 0);
                
                MessageBox.Show($"Standard execution result: {result}", "Success");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Standard execution failed: {ex.Message}", "Error");
            }
        }

        // Example 2: Ultra-stealthy execution using code replacement
        private void StealthyExecutionButton_Click(object sender, EventArgs e)
        {
            try
            {
                bool result = memory.Stealth.ExecuteByCodeReplacement<bool>(
                    (IntPtr)0x006C0310, 
                    CallingConvention.Cdecl, 
                    "Hello", 0, 0, 0, 0);
                
                MessageBox.Show($"Stealthy execution result: {result}", "Success");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Stealthy execution failed: {ex.Message}", "Error");
            }
        }

        // Example 3: Pointer redirection execution
        private void PointerRedirectionButton_Click(object sender, EventArgs e)
        {
            try
            {
                bool result = memory.Stealth.ExecuteByPointerRedirection<bool>(
                    (IntPtr)0x006C0310, 
                    CallingConvention.Cdecl, 
                    "Hello", 0, 0, 0, 0);
                
                MessageBox.Show($"Pointer redirection result: {result}", "Success");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Pointer redirection failed: {ex.Message}", "Error");
            }
        }

        // Example 4: Batch execution with multiple functions
        private void BatchExecutionButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Execute multiple functions with delays between them
                memory.Assembly.ExecuteVoid((IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                System.Threading.Thread.Sleep(100); // Small delay
                
                memory.Assembly.ExecuteVoid((IntPtr)0x006CC890, CallingConvention.Cdecl, "*", "I'm Working", 0x34EB40);
                System.Threading.Thread.Sleep(100); // Small delay
                
                // Execute another function
                bool result = memory.Assembly.Execute<bool>((IntPtr)0x006C0310, CallingConvention.Cdecl, "Batch", 1, 2, 3, 4);
                
                MessageBox.Show($"Batch execution completed. Last result: {result}", "Success");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Batch execution failed: {ex.Message}", "Error");
            }
        }

        // Example 5: Custom assembly code execution
        private void CustomCodeButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Example: Custom assembly that calls your function
                // This is more advanced - you'd need to generate the appropriate bytecode
                byte[] customCode = GenerateCustomAssembly();
                
                int result = memory.Assembly.ExecuteCustomCode<int>(customCode);
                
                MessageBox.Show($"Custom code result: {result}", "Success");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Custom code execution failed: {ex.Message}", "Error");
            }
        }

        // Helper method to generate custom assembly code
        private byte[] GenerateCustomAssembly()
        {
            // This is a simplified example - generates assembly to call your function
            var code = new System.Collections.Generic.List<byte>();
            
            // Push parameters (in reverse order for cdecl)
            // push 0
            code.AddRange(new byte[] { 0x6A, 0x00 });
            // push 0  
            code.AddRange(new byte[] { 0x6A, 0x00 });
            // push 0
            code.AddRange(new byte[] { 0x6A, 0x00 });
            // push 0
            code.AddRange(new byte[] { 0x6A, 0x00 });
            
            // Push string "Custom" (you'd need to allocate memory for this in practice)
            // For this example, we'll push a placeholder address
            code.Add(0x68); // push immediate
            code.AddRange(BitConverter.GetBytes(0x12345678)); // placeholder address
            
            // Call function at 0x006C0310
            code.Add(0xB8); // mov eax, immediate
            code.AddRange(BitConverter.GetBytes(0x006C0310));
            code.AddRange(new byte[] { 0xFF, 0xD0 }); // call eax
            
            // Clean up stack (5 parameters * 4 bytes = 20 bytes)
            code.AddRange(new byte[] { 0x83, 0xC4, 0x14 }); // add esp, 20
            
            // Return
            code.Add(0xC3); // ret
            
            return code.ToArray();
        }

        // Example 6: Error handling and fallback execution
        private void FallbackExecutionButton_Click(object sender, EventArgs e)
        {
            bool success = false;
            string method = "";
            bool result = false;

            // Try stealthy methods first, fallback to standard if needed
            try
            {
                method = "Code Replacement";
                result = memory.Stealth.ExecuteByCodeReplacement<bool>(
                    (IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                success = true;
            }
            catch
            {
                try
                {
                    method = "Pointer Redirection";
                    result = memory.Stealth.ExecuteByPointerRedirection<bool>(
                        (IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                    success = true;
                }
                catch
                {
                    try
                    {
                        method = "Standard Execution";
                        result = memory.Assembly.Execute<bool>(
                            (IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                        success = true;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"All execution methods failed: {ex.Message}", "Error");
                        return;
                    }
                }
            }

            if (success)
            {
                MessageBox.Show($"Execution successful using {method}. Result: {result}", "Success");
            }
        }

        // Example 7: Function with different return types
        private void DifferentReturnTypesButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Execute function expecting int return
                int intResult = memory.Assembly.Execute<int>(
                    (IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                
                // Execute function expecting float return (if applicable)
                float floatResult = memory.Assembly.Execute<float>(
                    (IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                
                // Execute function expecting pointer return
                IntPtr ptrResult = memory.Assembly.Execute<IntPtr>(
                    (IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);
                
                MessageBox.Show($"Int: {intResult}, Float: {floatResult}, Ptr: 0x{ptrResult.ToString("X")}", "Multiple Types");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Multiple types execution failed: {ex.Message}", "Error");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                memory?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}

/*
USAGE EXAMPLES SUMMARY:

1. STANDARD EXECUTION (Current method):
   memory.Assembly.Execute<bool>((IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);

2. ULTRA-STEALTHY CODE REPLACEMENT:
   memory.Stealth.ExecuteByCodeReplacement<bool>((IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);

3. POINTER REDIRECTION:
   memory.Stealth.ExecuteByPointerRedirection<bool>((IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);

4. VOID EXECUTION (No return value):
   memory.Assembly.ExecuteVoid((IntPtr)0x006C0310, CallingConvention.Cdecl, "Hello", 0, 0, 0, 0);

5. CUSTOM ASSEMBLY:
   memory.Assembly.ExecuteCustomCode<int>(customByteArray);

KEY BENEFITS:
- Same parameters as your current method
- Multiple stealth options
- Automatic fallback mechanisms
- No GetThreadContext calls
- Much harder to detect
*/
