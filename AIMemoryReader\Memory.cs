using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace AIMemoryReader
{
    // Wrapper class that maintains compatibility while using stealthy implementation
    public class Memory : IDisposable
    {
        private MemoryManager _memoryManager;
        private CodeExecutor _codeExecutor;
        private StealthExecutor _stealthExecutor;
        private bool _disposed = false;

        public Process Process => _memoryManager?.TargetProcess;
        public AssemblyExecutor Assembly { get; private set; }
        public StealthExecutor Stealth { get; private set; }

        public Memory(string processName)
        {
            try
            {
                // Initialize with new stealthy implementation
                _memoryManager = new MemoryManager(processName);
                _codeExecutor = new CodeExecutor(_memoryManager);
                _stealthExecutor = new StealthExecutor(_memoryManager);
                Assembly = new AssemblyExecutor(_codeExecutor);
                Stealth = _stealthExecutor;
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"Failed to initialize memory access: {ex.Message}", ex);
            }
        }

        ~Memory()
        {
            Dispose(false);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _memoryManager?.Dispose();
                }
                _disposed = true;
            }
        }

        // Compatibility methods that delegate to the new implementation
        public T Read<T>(IntPtr address) where T : struct
        {
            if (_disposed) throw new ObjectDisposedException(nameof(Memory));
            return _memoryManager.ReadValue<T>(address);
        }

        public string ReadString(IntPtr address, int size, Encoding encoding)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(Memory));
            return _memoryManager.ReadText(address, size, encoding);
        }

        public byte[] ReadBytes(IntPtr address, int count)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(Memory));
            return _memoryManager.ReadBytes(address, count);
        }

        public void Write<T>(IntPtr address, T value) where T : struct
        {
            if (_disposed) throw new ObjectDisposedException(nameof(Memory));
            _memoryManager.WriteValue(address, value);
        }

        public void WriteString(IntPtr address, string text, Encoding encoding)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(Memory));
            _memoryManager.WriteText(address, text, encoding);
        }

        public void WriteBytes(IntPtr address, byte[] bytes)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(Memory));
            _memoryManager.WriteBytes(address, bytes);
        }
    }

    // Updated AssemblyExecutor that uses the new stealthy implementation
    public class AssemblyExecutor
    {
        private readonly CodeExecutor _codeExecutor;

        public AssemblyExecutor(CodeExecutor codeExecutor)
        {
            _codeExecutor = codeExecutor ?? throw new ArgumentNullException(nameof(codeExecutor));
        }

        // Compatibility methods that delegate to the new implementation
        public T Execute<T>(IntPtr address, params object[] parameters) where T : struct
        {
            return _codeExecutor.ExecuteFunction<T>(address, parameters);
        }

        public T Execute<T>(IntPtr address, CallingConvention callingConvention, params object[] parameters) where T : struct
        {
            return _codeExecutor.ExecuteFunction<T>(address, callingConvention, parameters);
        }

        // Additional convenience methods
        public void ExecuteVoid(IntPtr address, params object[] parameters)
        {
            _codeExecutor.ExecuteVoidFunction(address, parameters);
        }

        public void ExecuteVoid(IntPtr address, CallingConvention callingConvention, params object[] parameters)
        {
            _codeExecutor.ExecuteVoidFunction(address, callingConvention, parameters);
        }
    }
}
